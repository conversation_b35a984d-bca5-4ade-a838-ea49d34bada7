<?php

declare(strict_types=1);

namespace App\Model\Orm\Order;

use App\Exceptions\LogicException;
use App\Model\DeliveryDate;
use App\Model\Orm\BackedEnumWrapper; // phpcs:ignore
use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\CurrencyContainer; // phpcs:ignore
use App\Model\Orm\JsonArrayHashContainer; // phpcs:ignore
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfigurationRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Class\ClassItem;
use App\Model\Orm\Order\Delivery\OrderDelivery;
use App\Model\Orm\Order\Delivery\PickupDeliveryInformation;
use App\Model\Orm\Order\Discount\DiscountItem;
use App\Model\Orm\Order\Gift\GiftItem;
use App\Model\Orm\Order\NumberSequence\OrderNumberGenerator;
use App\Model\Orm\Order\OrderStateHistory\OrderStateChange;
use App\Model\Orm\Order\Payment\BankTransferPaymentInformation;
use App\Model\Orm\Order\Payment\OrderPayment;
use App\Model\Orm\Order\Payment\PaymentState;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\Orm\Order\Promotion\PromotionItem;
use App\Model\Orm\Order\Voucher\VoucherItem;
use App\Model\Orm\PaymentMethod\PaymentMethodConfiguration;
use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductReview\ProductReview;
use App\Model\Orm\ProductVariant\Availability\CustomProductAvailability;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use App\Model\Orm\Traits\HasCache;
use App\Model\Orm\Traits\HasConsts;
use App\Model\Orm\User\User;
use App\Model\Orm\Voucher\Voucher;
use App\Model\Orm\VoucherCode\VoucherCode;
use App\Model\Promotion\Applicator\PromotionResult;
use App\Model\Promotion\PromotionApplicator;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalization;
use Brick\Math\BigDecimal;
use Brick\Money\Currency;
use Brick\Money\Money;
use DateTimeImmutable;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Entity\IEntity;
use Nextras\Orm\Relationships\OneHasMany;
use stdClass;
use App\Model\Orm\Order\Class\ClassItemRepository;

/**
 * @property-read int $id {primary}
 * @property-read string|null $cookieHash {default null}
 * @property string $hash {default null}
 * @property OrderState $state {wrapper BackedEnumWrapper}
 * @property OneHasMany<OrderStateChange> $stateChanges {1:m OrderStateChange::$order, cascade=[persist, remove]}
 * @property DateTimeImmutable|null $placedAt
 * @property string|null $orderNumber
 *
 * @property User|null $user {m:1 User::$orders}
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 * @property PriceLevel $priceLevel {m:1 PriceLevel, oneSided=true}
 *
 * @property string $email {default ''}
 * @property string $name {default ''}
 * @property string $street {default ''}
 * @property string $city {default ''}
 * @property string $zip {default ''}
 * @property string $phone {default ''}
 * @property string $note {default ''}
 * @property State $country {m:1 State, oneSided=true}
 * @property string|null $companyName
 * @property string|null $companyIdentifier
 * @property string|null $vatNumber
 * @property int|null $companyInvoicePay
 * @property Currency $currency {wrapper CurrencyContainer}
 * @property ArrayHash $metadata {container JsonArrayHashContainer}
 * @property DateTimeImmutable|null $syncedAt {default null}
 * @property DateTimeImmutable|null $syncStartedAt {default null}
 * @property string|null $extId {default null}
 * @property string|null $extState {default 'Nesynchronizovaná'}
 * @property DateTimeImmutable|null $syncTime {default null}
 * @property string|null $syncChecksum {default null}
 * @property int $heurekaSend {default 1}
 * @property string|null $heurekaResponse {default null}
 *
 * @property int|null $novikoHdId
 * @property int|null $novikoStatus
 * @property-read bool $readyToCloseInNoviko {virtual}
 * @property-read string $novikoIdObjednavkaPart {virtual} ID, ktere se posila do Novika jako parovaci mezi eshop <-> Noviko
 * @property OneHasMany<ProductItem> $products {1:m ProductItem::$order, cascade=[persist, remove]}
 * @property OneHasMany<ClassItem> $classEvents {1:m ClassItem::$order, cascade=[persist, remove]}
 * @property OneHasMany<VoucherItem> $vouchers {1:m VoucherItem::$order, cascade=[persist, remove]}
 * @property OneHasMany<DiscountItem> $discounts {1:m DiscountItem::$order, cascade=[persist, remove]}
 * @property OneHasMany<GiftItem> $gifts {1:m GiftItem::$order, cascade=[persist, remove]}
 * @property OneHasMany<PromotionItem> $promotions {1:m PromotionItem::$order, cascade=[persist, remove]}
 * @property OrderDelivery|null $delivery {1:1 OrderDelivery::$order, isMain=true, cascade=[persist, remove]}
 * @property OrderPayment|null $payment {1:1 OrderPayment::$order, isMain=true, cascade=[persist, remove]}
 * @property ProductReview[]|null $reviews {1:m ProductReview::$order}
 */
final class Order extends Entity implements OrderProxy
{

	use HasConsts;
	use HasCache;

	public const string ORDER_ACTION_CANCEL = 'cancel';
	public const string ORDER_ACTION_USER_EDIT = 'user_edit';
	public const string ORDER_ACTION_ITEMS_EDIT = 'items_edit';

	// ErpOrderStatus constants (converted from enum)
	public const int ERP_ORDER_STATUS_STORNO = -10; // Stornovaná
	public const int ERP_ORDER_STATUS_MERGE = -5; // Sloučená
	public const int ERP_ORDER_STATUS_INCOMPLETE = -1; // Nekompletní
	public const int ERP_ORDER_STATUS_CREATED = 0; // Vytvořená
	public const int ERP_ORDER_STATUS_OPEN = 1; // Neuzavřená (Rezervovaná)
	public const int ERP_ORDER_STATUS_DELAYING_STOCK = 11; // Ve skladu - odložená
	public const int ERP_ORDER_STATUS_DELAYING_COMPLETE = 14; // Odložená kompletace
	public const int ERP_ORDER_STATUS_TO_COMPLETE = 15; // Ke kompletaci
	public const int ERP_ORDER_STATUS_CLOSED_SUPPLIER = 16; // Objednávka je již u dodavatele uzavřena
	public const int ERP_ORDER_STATUS_DELAYING_DELIVERY = 19; // Odložení předání dopravci
	public const int ERP_ORDER_STATUS_TO_DELIVERY = 20; // K předání dopravci
	public const int ERP_ORDER_STATUS_FORWARDED_TO_DELIVERY = 21; // Předáno dopravci
	public const int ERP_ORDER_STATUS_RETURNED_BY_CARRIER = 22; // Vráceno dopravcem
	public const int ERP_ORDER_STATUS_DELIVERED = 29; // Expedovaná
	public const int ERP_ORDER_STATUS_STORNO_BEFORE = 101; // Uzavřeno jinak

	private DeliveryMethodConfigurationRepository $deliveryMethodConfigurationRepository;

	private PromotionApplicator $promotionApplicator;

	private ClassItemRepository $classItemRepository;

	public function __construct(
		Mutation $mutation,
		PriceLevel $priceLevel,
		State $country,
		User|null $user,
		Currency $currency,
	)
	{
		parent::__construct();
		$this->state = OrderState::Draft;
		$this->stateChanges->add(OrderStateChange::of($this, null, $this->state));
		$this->mutation = $mutation;
		$this->priceLevel = $priceLevel;
		$this->country = $country;
		$this->user = $user;
		$this->currency = $currency; // compatibility with other projects, in SA not used
		$this->setReadOnlyValue('cookieHash', md5(microtime(true) . ($this->user?->id ?? 0) . $this->mutation->id));
	}

	public function injectServices(
		DeliveryMethodConfigurationRepository $deliveryMethodConfigurationRepository,
		PromotionApplicator $promotionApplicator,
		ClassItemRepository $classItemRepository
	): void
	{
		$this->deliveryMethodConfigurationRepository = $deliveryMethodConfigurationRepository;
		$this->promotionApplicator = $promotionApplicator;
		$this->classItemRepository = $classItemRepository;
	}

	public function isDraft(): bool
	{
		return $this->state === OrderState::Draft || $this->state === OrderState::SavedForLater;
	}

	public function isEmpty(): bool
	{
		$sum = [$this->classEvents->countStored(), $this->products->countStored()];
		return array_sum($sum) === 0;
	}

	/**
	 * @return RefreshResult[]
	 */
	public function place(OrderNumberGenerator $numberGenerator): array
	{
		$changedItems = $this->refresh(); // this also checks that the order is in draft state
		if ($changedItems !== []) {
			return $changedItems;
		}

		if ($this->isEmpty()) {
			throw new LogicException('Cannot place an empty order.');
		}

		if ( ! ($this->delivery?->information?->isValid() ?? false)) {
			throw new LogicException('Cannot place an order without valid delivery information.');
		}

		if ($this->payment?->information === null) {
			throw new LogicException('Cannot place an order without valid payment information.');
		}

		$this->stateChanges->add(OrderStateChange::of($this, $this->state, OrderState::Placed));
		$this->setReadOnlyValue('state', OrderState::Placed);
		$this->setReadOnlyValue('placedAt', new DateTimeImmutable());

		$number = $numberGenerator->generateOrderNumber($this);
		$this->setReadOnlyValue('orderNumber', $number);
		$this->setReadOnlyValue('hash', $this->generateHash());

		return [];
	}

	public function cancel(?string $cancelReason = null): void
	{
		$this->stateChanges->add(OrderStateChange::of($this, $this->state, OrderState::Canceled, $cancelReason));
		$this->setReadOnlyValue('state', OrderState::Canceled);
	}

	public function decline(): void
	{
		$this->stateChanges->add(OrderStateChange::of($this, $this->state, OrderState::Declined));
		$this->setReadOnlyValue('state', OrderState::Declined);
	}

	public function dispatch(): void
	{
		$this->stateChanges->add(OrderStateChange::of($this, $this->state, OrderState::Dispatched));
		$this->setReadOnlyValue('state', OrderState::Dispatched);
	}

	public function prepare(): void
	{
		$this->stateChanges->add(OrderStateChange::of($this, $this->state, OrderState::Prepared));
		$this->setReadOnlyValue('state', OrderState::Prepared);
	}

	private function generateHash(): string
	{
		return bin2hex(random_bytes(20));
	}

	/**
	 * @return RefreshResult[]
	 */
	public function refresh(bool $withDelete = true, bool $onlyProducts = false): array
	{
		if ( ! $this->isDraft()) {
			throw new \LogicException('Cannot refresh OrderItem after order has been submitted.');
		}

		$changed = [];

		/** @var OrderItem|VoucherItem|ProductItem|OrderDelivery|OrderPayment|GiftItem $orderItem */
		foreach ($this->getItems() as $orderItem) {
			if ($onlyProducts) {
				$change = null;
				if ($orderItem instanceof ProductItem || $orderItem instanceof ClassItem) {
					$change = $orderItem->refresh($withDelete);
				}
			} else {
				$change = $orderItem->refresh($withDelete);
			}

			if (($change) !== null) {
				if ($orderItem instanceof ProductItem) {
					$changed[$orderItem->getPersistedId()] = $change;
				} elseif ($orderItem instanceof OrderDelivery) {
					$changed['delivery'] = $change;
				} elseif ($orderItem instanceof OrderPayment) {
					$changed['payment'] = $change;
				} elseif ($orderItem instanceof GiftItem) {
					$changed['gift-' . $orderItem->getPersistedId()] = $change;
				} elseif ($orderItem instanceof PromotionItem) {
					$changed['promotion-' . $orderItem->getPersistedId()] = $change;
				} else {
					$changed[] = $change;
				}

			}

		}

		return $changed;
	}

	public function initAutoItems(?Order $order = null): array
	{
		$changed = [];
		$appliedPromotions = $this->promotions->toCollection()->fetchPairs('promotionLocalization->id');

		/** @var PromotionResult $promotion */
		foreach ($this->promotionApplicator->getOrderPromotionsInfo($this)->getResults() as $promotion) {
			if ($promotion->savedMoney->isGreaterThan(0)) {

				if ( ! isset($appliedPromotions[$promotion->promotionLocalization->id])) {
					$promotionItem = PromotionItem::create($this, $promotion);

					$refreshResult                  = new RefreshResult($promotionItem);
					$refreshResult->updated         = true;
					$refreshResult->availableAmount = 1;
					//$refreshResult->addNullMessage();
					$refreshResult->addMessage('order_promotion_added');

				} else {
					if ($appliedPromotions[$promotion->promotionLocalization->id]->promotionUniqId !== $promotion->getUniqId()) {
						/** @var PromotionItem $persistedPromotion */
						$persistedPromotion = $appliedPromotions[$promotion->promotionLocalization->id];
						$persistedPromotion->promotionName = $promotion->promotionLocalization->getName();
						$persistedPromotion->promotionUniqId = $promotion->getUniqId();
						$persistedPromotion->promotionInfo = ArrayHash::from($promotion->toArray());
						$persistedPromotion->unitPrice = Price::from($persistedPromotion->getCurrentUnitPrice());

						$refreshResult = new RefreshResult($persistedPromotion);
						$refreshResult->updated = true;
						$refreshResult->availableAmount = 1;
						$refreshResult->addNullMessage();

					}
				}

				if (isset($refreshResult)) {
					$changed['promotion-' . $promotion->promotionLocalization->id] = $refreshResult;
				}

				unset($appliedPromotions[$promotion->promotionLocalization->id]);
			}
		}

		/** @var PromotionItem $toDeletePromotion */
		foreach ($appliedPromotions as $toDeletePromotion) {
			$refreshResult = new RefreshResult($toDeletePromotion);
			$refreshResult->removed = true;
			$refreshResult->addMessage('order_promotion_removed');
			$changed['promotion-' . $toDeletePromotion->promotionLocalization->id] = $refreshResult;
		}

		return $changed;
	}

	/**
	 * Returns real added quantity
	 *
	 * @param ProductVariant $variant
	 * @param int $amount
	 * @return int
	 */
	public function addProduct(ProductVariant $variant, int $amount = 1): int
	{
		if ($amount < 1) {
			throw new \LogicException();
		}

		/** @var ProductItem|null $existingItem */
		$existingItem = $this->products->toCollection()->getBy(['variant' => $variant]);

		if ($existingItem === null) {
			$item = ProductItem::create($this, $variant);
			$this->products->add($item);
			$item->setAmount($amount);

			return $item->amount;
		}

		$currentAmount = $existingItem->amount;
		$existingItem->setAmount($currentAmount + $amount);

		return abs($existingItem->amount - $currentAmount);
	}

	public function subtractProduct(ProductVariant $variant, int $amount = 1): null|int|ProductItem
	{
		if ($amount < 1) {
			throw new \LogicException();
		}

		/** @var ProductItem|null $existingItem */
		$existingItem = $this->products->toCollection()->getBy(['variant' => $variant]);

		if ($existingItem === null) {
			return null;
		}

		$currentAmount = $existingItem->amount;
		$newAmount = $currentAmount - $amount;

		if ($newAmount > 0) {
			$existingItem->setAmount($newAmount);
			return $newAmount;
		}

		return $this->removeProduct($variant);
	}

	public function removeProduct(ProductVariant $variant): ?ProductItem
	{
		/** @var ProductItem|null $existingItem */
		$existingItem = $this->products->toCollection()->getBy(['variant' => $variant]);

		if ($existingItem === null) {
			return null;
		}

		$this->products->remove($existingItem);
		return $existingItem;
	}

	/**
	 * Returns real added quantity
	 *
	 * @param ClassEvent $classEvent
	 * @param int $amount
	 * @return int
	 */
	public function addClass(Product $product, ?ClassEvent $classEvent, ?PriceLevel $priceLevel, int $amount = 1): int
	{
		if ($amount < 1) {
			throw new \LogicException();
		}

		/** @var ClassItem|null $existingItem */
		$existingItem = $this->classEvents->toCollection()->getBy(['product' => $product, 'classEvent' => $classEvent, 'priceLevel' => $priceLevel]);

		if ($existingItem === null) {
			$item = ClassItem::create($this, $product, $classEvent, $priceLevel);
			$this->classEvents->add($item);
			$item->setAmount($amount);

			return $item->amount;
		}

		$currentAmount = $existingItem->amount;
		$existingItem->setAmount($currentAmount + $amount);

		return abs($existingItem->amount - $currentAmount);
	}

	public function subtractClass(Product $product, ?ClassEvent $classEvent, ?PriceLevel $priceLevel, int $amount = 1): null|int|ClassItem
	{
		if ($amount < 1) {
			throw new \LogicException();
		}

		/** @var ClassItem|null $existingItem */
		$existingItem = $this->classEvents->toCollection()->getBy(['product' => $product, 'classEvent' => $classEvent, 'priceLevel' => $priceLevel]);

		if ($existingItem === null) {
			return null;
		}

		$currentAmount = $existingItem->amount;
		$newAmount = $currentAmount - $amount;

		if ($newAmount > 0) {
			$existingItem->setAmount($newAmount);
			return $newAmount;
		}

		return $this->removeClass($product, $classEvent, $priceLevel);
	}

	public function removeClass(Product $product, ?ClassEvent $classEvent, ?PriceLevel $priceLevel): ?ClassItem
	{
		/** @var ClassItem|null $existingItem */
		$existingItem = $this->classEvents->toCollection()->getBy(['product' => $product, 'classEvent' => $classEvent, 'priceLevel' => $priceLevel]);

		if ($existingItem === null) {
			return null;
		}

		$this->classEvents->remove($existingItem);
		return $existingItem;
	}

	public function addVoucher(VoucherCode $voucherCode): string|true
	{
		$existingItem = $this->vouchers->toCollection()->getBy(['voucherCode' => $voucherCode]);
		$response = VoucherCode::ERROR_ALREADY_IN_CART;
		if ($existingItem === null) {
			if (($response = $voucherCode->canBeApplied($this)) === true) {
				$item = VoucherItem::create($this, $voucherCode);
				$this->vouchers->add($item);
			}
		}
		return $response;
	}

	public function removeVoucher(VoucherCode $voucherCode): ?VoucherItem
	{
		$existingItem = $this->vouchers->toCollection()->getBy(['voucherCode' => $voucherCode]);

		if ($existingItem === null) {
			return null;
		}

		$this->vouchers->remove($existingItem);
		return $existingItem;
	}

	/**
	 * @param VoucherCode[] $exclude
	 */
	public function flushVouchers(array $exclude = [], bool $withGiftCertificates = false): array
	{
		$removed = [];
		foreach ($this->vouchers as $voucher) {
			if (!in_array($voucher->voucherCode, $exclude)) {
				if ($voucher->voucherCode->voucher->combination && !$withGiftCertificates) {
					continue;
				}

				$this->vouchers->remove($voucher);
				$removed[] = $voucher;
			}
		}
		return $removed;
	}

	public function addGift(GiftLocalization $giftLocalization): bool
	{
		$existingItem = $this->gifts->toCollection()->getBy(['giftLocalization' => $giftLocalization]);
		if ($existingItem === null) {
			if (($response = $giftLocalization->isAvailable($this)) === true) {
				$item = GiftItem::create($this, $giftLocalization);
				$this->gifts->add($item);
			}
		}
		return $response ?? false;
	}

	public function removeGift(GiftLocalization $giftLocalization): ?GiftItem
	{
		$existingItem = $this->gifts->toCollection()->getBy(['giftLocalization' => $giftLocalization]);

		if ($existingItem === null) {
			return null;
		}

		$this->gifts->remove($existingItem);
		return $existingItem;
	}


	public function flushGifts(): array
	{
		$removed = [];
		foreach ($this->gifts as $gift) {
			$this->gifts->remove($gift);
			$removed[] = $gift;
		}
		return $removed;
	}

	public function getVouchers(): ICollection
	{
		return $this->vouchers->toCollection();
	}


	public function getAppliedVoucherItem(): ?VoucherItem
	{
		return $this->loadCache($this->createCacheKey('appliedVoucherItem'), function () {
			return $this->vouchers->toCollection()->findBy([ICollection::OR, 'voucherCode->voucher->type' => [Voucher::TYPE_AMOUNT, Voucher::TYPE_PERCENT, Voucher::TYPE_FREE_DELIVERY], 'voucherType' => [Voucher::TYPE_AMOUNT, Voucher::TYPE_PERCENT, Voucher::TYPE_FREE_DELIVERY]])->fetch();
		});
	}

	/**
	 * @return ICollection<VoucherItem>
	 * @throws \Throwable
	 */
	public function getAppliedGiftVouchers(): ICollection
	{
		return $this->loadCache($this->createCacheKey('appliedGiftVouchers'), function () {
			return $this->vouchers->toCollection()->findBy([ICollection::OR, 'voucherCode->voucher->type' => [Voucher::TYPE_AMOUNT_COMBINATION], 'voucherType' => [Voucher::TYPE_AMOUNT_COMBINATION]]);
		});
	}

	public function hasGiftVouchers(): bool
	{
		return count($this->getAppliedGiftVouchers()) > 0;
	}

	/**
	 * @return ICollection<ProductItem>
	 */
	public function getProducts(): ICollection
	{
		return $this->products->toCollection();
	}

	/**
	 * @return ICollection<ClassItem>
	 */
	public function getClassEvents(): ICollection
	{
		return $this->classEvents->toCollection();
	}


	public function getProductsIds(): array
	{
		return $this->products->toCollection()->fetchPairs(null, 'variant->product->id') + $this->classEvents->toCollection()->fetchPairs(null, 'product->id');
	}

	public function hasProductId(int|array $productId): bool
	{
		if (is_int($productId)) {
			return in_array($productId, $this->getProductsIds());
		}

		foreach ($productId as $pid) {
			if (!$this->hasProductId($pid)) {
				return false;
			}
		}

		return true;
	}

	public function containsProductId(int|array $productId): bool
	{
		if (is_int($productId)) {
			return in_array($productId, $this->getProductsIds());
		}

		foreach ($productId as $pid) {
			if ($this->hasProductId($pid)) {
				return true;
			}
		}

		return false;
	}

	public function getPromotions(): ICollection
	{
		return $this->promotions->toCollection();
	}

	public function getGifts(): ICollection
	{
		return $this->gifts->toCollection();
	}

	public function hasGift(): bool
	{
		return $this->getGifts()->countStored() > 0;
	}

	/**
	 * @return OrderItem[]|IEntity[]
	 */
	public function getItems(): array
	{
		$products = $this->getProducts()->fetchAll();
		$classEvents = $this->getClassEvents()->fetchAll();
		$vouchers = $this->getVouchers()->fetchAll();
		$gifts = $this->getGifts()->fetchAll();
		$promotions = $this->getPromotions()->fetchAll();
		$delivery = $payment = [];

		if ($this->hasDelivery()) {
			$delivery[] = $this->getDelivery();
		}

		if ($this->hasPayment()) {
			$payment[] = $this->getPayment();
		}

		return array_merge($products, $classEvents, $vouchers, $gifts, $promotions, $delivery, $payment);
	}

	/**
	 * @return OrderItem[]
	 */
	public function getBuyableItems(?int $limitBy = null, bool $checkAvailability = false, bool $checkDeleted = false): array
	{
		$products = $this->getProducts()->fetchAll();

		if ($checkAvailability || $checkDeleted) {
			$filteredProducts = [];
			foreach ($products as $item) {
				$variant = $item->variant;
				$product = $variant->product;

				if ($checkDeleted && $product === null) {
					continue;
				}

				if ($checkAvailability) {
					if (!$product->isPublished() || $variant->isOld) {
						continue;
					}

					if (!in_array($variant->productAvailability->getType(), [
						CustomProductAvailability::TYPE_ON_STOCK,
						CustomProductAvailability::TYPE_ON_STOCK_SUPPLIER,
						CustomProductAvailability::TYPE_PREORDER,
						CustomProductAvailability::TYPE_TO_ORDER,
					])) {
						continue;
					}

					if (!$variant->productAvailability->hasPrice($this->mutation, $this->priceLevel, $this->mutation->getFirstState())) {
						continue;
					}
				}

				$filteredProducts[] = $item;
			}

			$products = $filteredProducts;
		}

		if ($limitBy !== null) {
			return array_slice($products, 0, $limitBy);
		}

		return $products;
	}

	public function getDelivery(): OrderDelivery|null
	{
		return $this->delivery;
	}

	public function getPickupPointId(): ?string
	{
		if ($this->delivery?->information instanceof PickupDeliveryInformation) {
			return $this->delivery->information->pickupPointId;
		}
		return null;
	}

	public function hasDelivery(): bool
	{
		return $this->getDelivery() !== null;
	}

	public function hasInvoice(): bool
	{
		return false;
		//todo return $this->() !== null;
	}

	public function setDelivery(OrderDelivery|DeliveryMethodConfiguration|null $item, bool $ignoreState = false): void
	{
		if ( ! $this->isDraft() && !$ignoreState) {
			throw new \LogicException('Cannot modify non-draft order.');
		}
		assert($item instanceof OrderDelivery || $item === null);
		$this->delivery = $item;
	}

	public function createDelivery(DeliveryMethodConfiguration $delivery): OrderDelivery
	{
		return OrderDelivery::create($this, $delivery);
	}


	public function getPayment(): OrderPayment|null
	{
		return $this->payment;
	}

	public function hasPayment(): bool
	{
		return $this->getPayment() !== null;
	}

	public function setPayment(OrderPayment|PaymentMethodConfiguration|null $payment): void
	{
		assert($payment instanceof OrderPayment || $payment === null);
		//if ( ! $this->isDraft()) {
			//throw new \LogicException('Cannot modify non-draft order.');
		//}

		$this->payment = $payment;
	}

	public function createPayment(PaymentMethodConfiguration $payment): OrderPayment
	{
		return OrderPayment::create($this, $payment);
	}

	public function getTotalProductPrice(bool $withVat = false, ?array $productIds = null): Money
	{
		$price = Money::of(0, $this->currency->getCurrencyCode());

		/** @var ProductItem $orderItem */
		foreach ($this->getProducts() as $orderItem) {
			if ($productIds !== null && !in_array($orderItem->variant->product->id, $productIds)) {
				continue;
			}

			$price = $price->plus($withVat ? $orderItem->totalPriceVat : $orderItem->totalPrice);
		}

		if ($price->isLessThan(0)) {
			$price = Money::of(0, $this->currency->getCurrencyCode());
		}

		return Price::from($price)->asMoney();
	}

	public function getTotalProductPriceVat(): Money
	{
		return $this->getTotalProductPrice(withVat: true);
	}

	public function getTotalProductIdsPriceVat(array $productIds): Money
	{
		return $this->getTotalProductPrice(withVat: true, productIds: $productIds);
	}

	public function getTotalPrice(bool $withVat = false, bool $withDelivery = false, bool $includeGiftCertificates = true, bool $withPromotions = true, bool $withFreeDeliveryVoucher = true, ?int $precision = null): Money
	{
		return $this->loadCache($this->createCacheKey('total_price', ...func_get_args()), function () use ($withVat, $withDelivery, $includeGiftCertificates, $withPromotions, $withFreeDeliveryVoucher, $precision) {
			$price = Money::of(0, $this->currency->getCurrencyCode());
			if ($this->getTotalCount(onlyProducts: false) > 0) {
				/** @var OrderItem $orderItem */
				foreach ($this->getItems() as $orderItem) {
					if ($withDelivery === false && ($orderItem instanceof OrderDelivery || $orderItem instanceof OrderPayment)) {
						continue;
					}

					if ($withPromotions === false && ($orderItem instanceof PromotionItem)) {
						continue;
					}

					if (!$includeGiftCertificates &&
						(
							($orderItem instanceof VoucherItem && $orderItem->voucherCode !== null && $orderItem->voucherCode->voucher->type === Voucher::TYPE_AMOUNT_COMBINATION) ||
							($orderItem instanceof VoucherItem && $orderItem->voucherType === Voucher::TYPE_AMOUNT_COMBINATION)
						)
					) {
						continue;
					}

					if (!$withFreeDeliveryVoucher && (
							($orderItem instanceof VoucherItem && $orderItem->voucherCode !== null && $orderItem->voucherCode->voucher->type === Voucher::TYPE_FREE_DELIVERY) ||
							($orderItem instanceof VoucherItem && $orderItem->voucherType === Voucher::TYPE_FREE_DELIVERY)
						)
					) {
						continue;
					}

					$price = $price->plus($withVat ? $orderItem->getTotalPriceVat($precision) : $orderItem->getTotalPrice($precision));
				}
			}

			if ($price->isLessThan(0)) {
				$price = Money::of(0, $this->currency->getCurrencyCode());
			}

			return Price::from($price)->asMoney($precision);
		});
	}

	public function getTotalGiftCertificatesPrice(bool $withVat = true): Money
	{
		return $this->loadCache($this->createCacheKey('total_gift_certificates_price', ...func_get_args()), function () use ($withVat) {
			$price = Money::of(0, $this->currency->getCurrencyCode());

			foreach ($this->vouchers as $voucherItem) {
				if ($voucherItem->voucherCode->voucher->type === Voucher::TYPE_AMOUNT_COMBINATION) {
					$price = $price->plus($withVat ? $voucherItem->totalPriceVat : $voucherItem->totalPrice);
				}
			}

			return Price::from($price)->asMoney();
		});
	}

	public function getTotalPriceWithDelivery(?int $precision = null): Money
	{
		return $this->getTotalPrice(withDelivery: true, precision: $precision);
	}

	public function getTotalPriceVat(
		bool $withDelivery = false,
		bool $includeGiftCertificates = true,
		bool $withFreeDeliveryVoucher = true,
		?int $precision = null
	): Money
	{
		return $this->getTotalPrice(withVat: true, withDelivery: $withDelivery, includeGiftCertificates: $includeGiftCertificates, withFreeDeliveryVoucher: $withFreeDeliveryVoucher, precision: $precision);
	}

	public function getTotalDiscountVat(): Money
	{
		return $this->loadCache($this->createCacheKey('total_discount_vat'), function () {
			$discount = Money::of(0, $this->getCurrency());

			// applied vouchers
			if (($voucherItem = $this->getAppliedVoucherItem()) !== null && $voucherItem->voucherCode->voucher->type !== Voucher::TYPE_FREE_DELIVERY) {
				$discount = $discount->plus($voucherItem->totalPriceVat->multipliedBy('-1'));
			}

			// product with discount - deactivated 30.5.2024
			//$discount = $discount->plus($this->getProductItemsDiscountPrice()->multipliedBy('-1'));

			// TODO: akcni cena

			foreach ($this->promotions as $promotion) {
				$discount = $discount->plus($promotion->totalPriceVat);
			}

			return Price::from($discount)->asMoney();
		});
	}

	public function getProductItemsDiscountPrice(): Money
	{
		return $this->loadCache('product_items_discount_price', function () {
			$discount = Money::of(0, $this->getCurrency());
			/** @var ProductItem $productItem */
			foreach ($this->getProducts() as $productItem) {
				$productItem->variant->product->priceVat($this->mutation, $this->priceLevel, $this->country);
				if (($discountItem = $productItem->variant->product->getPriceInfo($this->mutation, $this->priceLevel, $this->country)->getDiscountDelta()) !== null) {
					$discount = $discount->plus($discountItem)->multipliedBy($productItem->amount);
				}
			}
			return $discount;
		});
	}

	public function hasFreeDeliveryVoucher(): bool
	{
		return ($voucher = $this->getAppliedVoucherItem()) !== null && $voucher->voucherCode->voucher->type === Voucher::TYPE_FREE_DELIVERY;
	}

	public function hasItemWithForcedFreeDelivery(): bool
	{
		foreach ($this->getProducts() as $productItem) {
			$productVariant = $productItem->variant;
			$now = new \Nextras\Dbal\Utils\DateTimeImmutable();
			if ($productVariant->isFreeTransportForced
				&& $productVariant->freeTransportForcedFrom <= $now
				&& $productVariant->freeTransportForcedTo >= $now
			) {
					return true;
			}
		}

		return false;
	}

	public function getTotalOriginalPriceVat(): Money
	{
		$price = Money::of(0, $this->currency->getCurrencyCode());

		/** @var ProductItem $orderItem */
		foreach ($this->getProducts() as $orderItem) {
			$price = $price->plus($orderItem->totalPriceVat);
		}

		$price = $price->plus($this->getProductItemsDiscountPrice());

		if ($price->isLessThan(0)) {
			$price = Money::of(0, $this->currency->getCurrencyCode());
		}

		return Price::from($price)->asMoney();
	}

	public function getTotalPriceWithDeliveryVat(?int $precision = null): Money
	{
		return $this->getTotalPriceVat(withDelivery: true, precision: $precision);
	}

	public function getTotalCount(bool $onlyProducts = true): int
	{
		$count = [0];
		/** @var ProductItem $product */
		foreach ($this->getProducts() as $product) {
			$count[] = $product->amount;
		}

		foreach ($this->getClassEvents() as $classItem) {
			$count[] = $classItem->amount;
		}

		if (!$onlyProducts) {
			/** @var GiftItem $gift */
			foreach ($this->getGifts() as $gift) {
				$count[] = $gift->amount;
			}
		}

		return array_sum($count);
	}

	public function getTotalWeight(): BigDecimal
	{
		return $this->loadCache($this->createCacheKey('totalWeight', $this), function () {
			$weight = BigDecimal::of(0);

			/** @var ProductItem $productItem */
			foreach ($this->getProducts() as $productItem) {
				$weight = $weight->plus($productItem->variant->getWeight()->multipliedBy($productItem->amount));
			}

			return $weight;
		});
	}

	public function getCurrency(): Currency
	{
		return $this->currency;
	}

	public function getUserEntity(): ?User
	{
		return $this->user;
	}

	public function getPaymentAccount(): ?stdClass
	{
		return $this->payment->paymentMethod->cf?->paymentAccount ?? null;
	}

	public function useDeliveryAddress(): bool
	{
		return $this->delivery->deliveryMethod->getDeliveryMethod()->useDeliveryAddress();
	}

	public function getVariableSymbol(): ?string
	{
		if ($this->payment->information instanceof BankTransferPaymentInformation) {
			return $this->payment->information->variableSymbol;
		}
		return null;
	}

	public function isPaid(): bool
	{
		if ($this->isDraft()) {
			throw new \LogicException('Draft order can not be paid.');
		}

		return in_array($this->payment->information->state, [PaymentState::Completed]);
	}

	public function isCanceled(): bool
	{
		if ($this->isDraft()) {
			throw new \LogicException('Draft order can not be canceled.');
		}

		return $this->state === OrderState::Canceled; // or $this->stateChanges->toCollection()->findBy(['to' => OrderState::Canceled])->countStored() > 0;
	}

	public function isDeclined(): bool
	{
		if ($this->isDraft()) {
			throw new \LogicException('Draft order can not be declined.');
		}

		return $this->state === OrderState::Declined; // or $this->stateChanges->toCollection()->findBy(['to' => OrderState::Declined])->countStored() > 0;
	}


	public function setMetadata(array|ArrayHash $data): self
	{
		$stored = $this->getValue('metadata');
		foreach ($data as $name => $value) {
			if (!is_iterable($value)) {
				$stored->{$name} = $value;
			} else {
				foreach ($value as $k => $v) {
					$stored->{$name}[$k] = $v;
				}
			}
		}

		$this->setValue('metadata', $stored);

		return $this;
	}

	public function hasElectronicProduct(bool $strict = false): bool
	{
		return $this->loadCache($this->createCacheKey('hasElectronicProduct', $this, $strict), function () use ($strict) {
			foreach ($this->products as $product) {
				if ( ! $strict && $product->variant->product->isElectronic) {
					return true;
				} elseif ($strict && ! $product->variant->product->isElectronic) {
					return false;
				}
			}

			return true;
		});
	}

	public function hasCertificate(bool $strict = false): bool
	{
		return $this->loadCache($this->createCacheKey('hasCertificate', $this, $strict), function () use ($strict) {
			foreach ($this->products as $product) {
				if ( ! $strict && $product->variant->product->isCertificate) {
					return true;
				} elseif ($strict && ! $product->variant->product->isCertificate) {
					return false;
				}
			}

			return true;
		});
	}

	public function isRegistrationRequired(): bool
	{
		return $this->loadCache($this->createCacheKey('isRegistrationRequired', $this), function () {
			return array_any($this->products->toCollection()->fetchAll(), fn($product) => $product->variant->product->isElectronic && ! $product->variant->product->isCertificate);
		});
	}

	public function getCountry(): State
	{
		return $this->country;
	}

	public function getWorstDeliveryDate(DeliveryMethodConfiguration $deliveryMethodConfiguration): null|false|DeliveryDate
	{
		return null;
	}

	/**
	 * @return ICollection<ClassItem>
	 */
	public function getAllClasses(): ICollection
	{
		return $this->classItemRepository->getAllClasses(orderHash: $this->hash);
	}

	protected function getterReadyToCloseInNoviko(): bool
	{
		// close order if it is in no subscription, set false if expedition date is in future or if it is cash on delivery and expedition date is in future - this is fix for first order
		return true;
	}

	protected function getterHasTrackingPackageNumber(): bool
	{
		//return in_array($this->transportType, [self::TRANSPORT_DPD, self::TRANSPORT_ZASILKOVNA, self::TRANSPORT_PPL, self::TRANSPORT_PPL_PARCEL], true);
		//TODO MichalK - subscription
		return true;
	}

	protected function getterIsSubscriptionOrder(): bool
	{
		//return $this->subscriptionOrder !== null;
		//TODO MichalK - subscription
		return false;
	}

	protected function getterNovikoIdObjednavkaPart(): ?string
	{
		return $this->orderNumber;
	}

	public function isActionAllowed(string $action): bool
	{
		if ($this->novikoStatus === self::ERP_ORDER_STATUS_STORNO) {
			return false;
		}
		if ($action === self::ORDER_ACTION_CANCEL) {
			return $this->novikoStatus < self::ERP_ORDER_STATUS_TO_DELIVERY;
		} elseif ($action === self::ORDER_ACTION_ITEMS_EDIT) {
			return $this->novikoStatus < self::ERP_ORDER_STATUS_DELAYING_STOCK;
		} elseif ($action === self::ORDER_ACTION_USER_EDIT) {
			return $this->novikoStatus < self::ERP_ORDER_STATUS_TO_DELIVERY;
		}
		return true;
	}

}
