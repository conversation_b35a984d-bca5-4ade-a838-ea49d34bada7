<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Order;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\Order\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\Order\Components\DataGrid\DataGridFactory;
use App\AdminModule\Presenters\Order\Components\OrderCancelForm\OrderCancelForm;
use App\AdminModule\Presenters\Order\Components\OrderCancelForm\OrderCancelFormFactory;
use App\AdminModule\Presenters\Order\Components\OrderForm\OrderForm;
use App\AdminModule\Presenters\Order\Components\OrderForm\OrderFormFactory;
use App\AdminModule\Presenters\Order\Components\OrderItemsForm\OrderItemsForm;
use App\AdminModule\Presenters\Order\Components\OrderItemsForm\OrderItemsFormFactory;
use App\Model\Erp\Enum\ErpOrderCancelReason;
use App\Model\Erp\Enum\ErpOrderStatus;
use App\Model\Erp\ErpOrderService;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderState;
use Nextras\Orm\Collection\ICollection;

final class OrderPresenter extends BasePresenter
{
	/**
	 * @var ICollection<Order>
	 */
	private ICollection $orders;

	private Order|null $order;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly OrderFormFactory $orderFormFactory,
		private readonly OrderCancelFormFactory $orderCancelFormFactory,
		private readonly OrderItemsFormFactory $orderItemsFormFactory,
	)
	{
		parent::__construct();
	}

	public function actionDefault(): void
	{
		$this->orders = $this->orm->order
			->findBy(['state!=' => [OrderState::Draft, OrderState::SavedForLater]]);
			//->orderBy('placedAt', ICollection::DESC);
	}

	public function actionDetail(int $id): void
	{
		$this->order = $this->orm->order->getById($id);

		if ($this->order === null) {
			$this->redirect('default');
		}
	}

	public function renderDetail(): void
	{
		$this->template->order = $this->order;

		$novikoStatusEnum = ErpOrderStatus::toArray();
		$this->template->novikoStatusName = isset($novikoStatusEnum[$this->order->novikoStatus]) ? $novikoStatusEnum[$this->order->novikoStatus] . ' (' . $this->order->novikoStatus . ')' : null;
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create($this->orders);
	}

	protected function createComponentOrderForm(): OrderForm
	{
		return $this->orderFormFactory->create($this->order, $this->userEntity);
	}

	protected function createComponentOrderCancelForm(): OrderCancelForm
	{
		return $this->orderCancelFormFactory->create($this->order);
	}

	protected function createComponentOrderItemsForm(): OrderItemsForm
	{
		return $this->orderItemsFormFactory->create($this->order, $this->userEntity);
	}
}
