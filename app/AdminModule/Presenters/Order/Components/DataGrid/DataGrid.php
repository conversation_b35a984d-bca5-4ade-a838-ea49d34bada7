<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Order\Components\DataGrid;

use App\Model\ConfigService;
use App\Model\Erp\Enum\ErpOrderStatus;
use App\Model\Erp\ErpOrderService;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Order\Payment\PaymentState;
use App\Model\Orm\Order\Sync\OrderSyncType;
use App\Model\Orm\Orm;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\HasMutationColumn;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\DbalCollection;
use Nextras\Orm\Collection\ICollection;
use Symfony\Component\Messenger\MessageBusInterface;

/**
 * @property-read DefaultTemplate $template
 */
final class DataGrid extends Control
{
	use HasMutationColumn;

	/**
	 * @param ICollection<Order> $orders
	 */
	public function __construct(
		private readonly ICollection $orders,
		private readonly Translator $translator,
		private readonly Orm $orm,
		private readonly ErpOrderService $erpOrderService,
	) {}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}

	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$grid->setDataSource($this->orders);
		$grid->setColumnsHideable();
		$grid->addColumnText('id', 'id')->setDefaultHide();

		$grid->addColumnText('orderNumber', 'no.')
			->setSortable()
			->setFilterText();

		$grid->setDefaultSort(['placedAt' => 'DESC']);

		$states = []; //$this->connection->query('SELECT DISTINCT `extState` FROM `order` WHERE `state` != %s', OrderState::Draft)->fetchPairs('extState', 'extState');

		//$this->addColumnMutation($grid);
		$grid->addColumnDateTime('placedAt', 'Order date')->addCellAttributes(['width'=>240])->setFormat('d.m.Y H:i:s')
		     ->setSortable()->setFilterDateRange();

		$grid->addColumnDateTime('syncedAt', 'syncedAt')->setFormat('d.m.Y H:i:s')->setDefaultHide();

		$grid->addColumnText('state', 'order_state')
				->setRenderer(
			fn(Order $order) => $order->state->name
			)
			->setFilterMultiSelect(OrderState::toArray(), '---')
				->setCondition(function(DbalCollection $collection, $value) {
	                if ($value !== null && $value !== '') {
						$collection->getQueryBuilder()
							->andWhere('order.state in %s[]', (array)$value);
	                }
                });
		$ErpOrderStatus = ErpOrderStatus::toArray();
		$grid->addColumnText('noviko_state', 'noviko_state')
				->setRenderer(
			fn(Order $order) => $ErpOrderStatus[$order->novikoStatus] ?? '?'
			)
			->setFilterMultiSelect(ErpOrderStatus::toArray(), '---')
				->setCondition(function(DbalCollection $collection, $value) {
	                if ($value !== null && $value !== '') {
						$collection->getQueryBuilder()
							->andWhere('order.novikoStatus in %i[]', (array)$value);
	                }
                });

		$grid->addColumnText('delivery', 'delivery')
			->setRenderer(
				fn(Order $order) => $order->delivery?->deliveryMethod?->name
				)
			->setFilterMultiSelect($this->orm->deliveryMethod->findAll()->fetchPairs('id', 'name'))
				->setCondition(function(DbalCollection $collection, $value) {
	                if ($value !== null && $value !== '') {
						$collection->getQueryBuilder()
							->joinLeft('[order_delivery] as od', '[od.id] = [order.deliveryId]')
							->andWhere('od.deliveryMethodId in %i[]', (array)$value);
	                }
                });

		$grid->addColumnText('payment', 'payment')
			->setRenderer(
				fn(Order $order) => $order->payment?->paymentMethod?->name
				)
			->setFilterMultiSelect($this->orm->paymentMethod->findAll()->fetchPairs('id', 'name'))
				->setCondition(function(DbalCollection $collection, $value) {
	                if ($value !== null && $value !== '') {
						$collection->getQueryBuilder()
							->joinLeft('[order_payment] as op', '[op.id] = [order.paymentId]')
							->andWhere('op.paymentMethodId in %i[]', (array)$value);
	                }
                });

		$grid->addColumnText('payment_status', 'payment_status')
	    ->setRenderer(function(Order $order) {
	        if ($order->payment === null || $order->payment->information === null) {
	            return 'No payment';
	        }

	        $paymentState = $order->payment->information->state;
	        return $paymentState->value;
	    })
		->setFilterMultiSelect(PaymentState::toArray())
			->setCondition(function(DbalCollection $collection, $value) {
	                if ($value !== null && $value !== '') {
						$collection->getQueryBuilder()
							->joinLeft('[order_payment] as opp', '[opp.id] = [order.paymentId]')
							->joinLeft('[order_payment_information] as pi', '[pi.id] = [opp.informationId]')
							->andWhere('pi.state in %s[]', (array)$value);
	                }
        });

		$grid->addColumnText('name', 'User Name')->setFilterText();
		$grid->addColumnText('email', 'email')->setFilterText();
		$grid->addColumnText('phone', 'order_customer_phone')->setDefaultHide()->setFilterText();

		$grid->addColumnNumber('price', 'price_sum')
			->setRenderer(fn(Order $order) => $order->getTotalPriceVat(withDelivery: true, includeGiftCertificates: false)->formatTo('cs'));
			//->setSortable();

		$grid->addAction('sync', 'sync', 'sync!')->addAttributes(['data-ignore-edit' => ''])->setClass('btn btn-xs btn-warning ajax');
		$grid->addAction('detail', 'Detail', ':detail')->setClass('btn btn-xs btn-primary');

		$grid->allowRowsAction('sync', fn(Order $order) => $order->syncedAt === null);

		$grid->setTranslator($this->translator);

		$grid->setItemsPerPageList([30, 50, 100], false);
		//$grid->setStrictSessionFilterValues(false);

		return $grid;
	}

	public function handleSync(int $id): void
	{
		$order = $this->orm->order->getById($id);
		if ($order !== null) {
			$this->erpOrderService->createOrder($order, OrderSyncType::OrderCreateManual);
		}


		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this');
		}
		$this['grid']->reload();
	}
}
