<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Order\Components\OrderCancelForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\Erp\Enum\ErpOrderCancelReason;
use App\Model\Erp\ErpOrderService;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderModel;
use App\Model\Orm\Orm;
use App\Model\Translator;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;

/**
 * @property-read DefaultTemplate $template
 */
final class OrderCancelForm extends Control
{
	public function __construct(
		private Order $order,
		private readonly Orm $orm,
		private readonly Translator $translator,
		private readonly TranslatorDB $translatorDB,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly OrderModel $orderModel,
		private readonly ErpOrderService $erpOrderService,
	)
	{
	}

	public function render(): void
	{
		$this->template->object = $this->order;
		$this->template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$this->template->add('templates', RS_TEMPLATE_DIR);

		$this->template->order = $this->order;
		$this->template->orm = $this->orm;
		$this->template->translatorDB = $this->translatorDB;
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/orderCancelForm.latte');
	}

	protected function createComponentForm(): Form
	{
		$form = new Form();

		$form->setTranslator($this->translator);
		$form->addSelect('cancelReason', 'cancel_reason', ErpOrderCancelReason::toArray());
		$form->addSubmit('cancel');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onValidate[] = $this->formValidate(...);
		$form->onError[] = [$this, 'formError'];

		return $form;
	}

	private function formValidate(Form $form, ArrayHash $values): void
	{
		//refresh order status
		$ret = $this->erpOrderService->syncStatus($this->order);

		if (!$ret) {
			$form->addError($this->erpOrderService->getLastMessage()->text);
		} else {
			//load updated order
			$this->order = $this->orm->order->getById($this->order->id);
		}

		if (!$this->order->isActionAllowed(Order::ORDER_ACTION_CANCEL)) {
			$form->addError('order_edit_not_allowed');
		}
	}

	public function formError(Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function formSucceeded(Form $form, ArrayHash $values): void
	{
		$orderId = $this->order->id;
		$this->order = $this->erpOrderService->cancelOrder($this->order);
		if ($this->order->novikoStatus === Order::ERP_ORDER_STATUS_STORNO) {
			$this->orderModel->cancel($this->order, true, empty($values->cancelReason) ? null : $values->cancelReason);
		}
		//$this->orm->persistAndFlush($this->order);
		$this->presenter->redirect('detail', ['id' => $orderId]);
	}

	public function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
